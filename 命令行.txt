


python trade.py --weight_type best_ric --output_dir gp/cyb1 --top_n 10 --run_id V1 --start_date 2025-06-01 --end_date 2025-07-16 --data_path tushare_data_cyb/stock_factors_cyb.csv


python trade.py --weight_type best_ic --output_dir gp/cyb1 --top_n 10 --run_id V1 --start_date 2025-06-01 --end_date 2025-07-16 --data_path tushare_data_cyb/stock_factors_cyb.csv


python trade.py --weight_type last --output_dir gp/cyb2 --top_n 10 --run_id V2 --start_date 2025-06-01 --end_date 2025-07-16 --data_path tushare_data_cyb/stock_factors_cyb.csv






python trade.py --weight_type best_ic --output_dir gp/300 --top_n 10 --run_id V1 --start_date 2025-06-01 --end_date 2025-07-16 --data_path tushare_data_hs300/stock_factors_hs300_train.csv





